# vim: ft=yaml

stop_vertex_9:
  cmd.run:
    - names:
      - /vertex/oseries-pos-9/tomcat/bin/shutdown.sh

/vertex/backup:
  file.directory:
    - user: root
    - group: users      

backup_vertex_9_extract:
  cmd.run: 
    - names: 
      - mv /vertex/oseries-pos-9/data/taxdata/* /vertex/backup/

copy_vertex_9_extract:
  file.recurse:
    - user: root
    - group: users
    - file_mode: 777
    - name: /vertex/oseries-pos-9/data/taxdata
    - source: salt://branch/monthly-vertex-9-extract/extract
    - require: 
      - stop_vertex_9

set_vertex_9_permissions:
  cmd.run:
    - names:
      - chmod -R 777 /vertex
      - chown -R root:users /vertex

start_vertex_9:
  cmd.run:
    - names:
      - /vertex/restartVertex.sh
    - require:
      - copy_vertex_9_extract
      - set_vertex_9_permissions
