# vim: ft=yaml
{# set prev_squid_conf = salt['cmd.shell']('ls -lrt /etc/squid/squid.conf.[0-9]* | tail -1 | awk \'{print $9}\'') #}

stop_beanstore_squidservice:
  service.dead:
    - name: squid-beanstore

rollback_squid_beanstore_conf:
  cmd.run:
    - names:
      - systemctl disable squid-beanstore.service
      - rm /etc/squid/squid.conf.beanstore-proxy
      - rm /etc/systemd/system/squid-beanstore.service
