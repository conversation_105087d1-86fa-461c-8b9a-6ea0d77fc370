# Solidity Learning Android App

An interactive Android application designed to teach Solidity programming through hands-on lessons and quizzes. The app features 5 progressive levels covering essential smart contract development concepts.

## Features

### 📚 5 Learning Levels
1. **ERC-20 Token Contract** - Basic token creation and JavaScript testing
2. **ERC-721 NFT Contract** - Non-fungible token development and testing
3. **Crowdfunding via NFT Minting** - Advanced contract interactions
4. **DAO Contract** - Decentralized governance and voting mechanisms
5. **AMM Contract & React Frontend** - Automated Market Maker with frontend integration

### 🎯 Interactive Learning
- **Code Lessons**: Syntax-highlighted Solidity code with detailed explanations
- **Fill-in-the-Blank Questions**: Interactive code completion exercises
- **Multiple Choice Quizzes**: Concept understanding verification
- **Progress Tracking**: Monitor advancement through all levels

### 🛠 Technical Features
- Modern Android architecture with Kotlin
- Syntax highlighting for Solidity code
- Persistent progress tracking
- Material Design UI components
- Responsive layouts for different screen sizes

## Project Structure

```
quiz_app/
├── app/
│   ├── src/main/
│   │   ├── java/com/solidityquiz/app/
│   │   │   ├── adapters/          # RecyclerView adapters
│   │   │   ├── data/              # Data management and lesson content
│   │   │   ├── models/            # Data models
│   │   │   ├── MainActivity.kt    # Main level selection screen
│   │   │   ├── LessonActivity.kt  # Code lesson display
│   │   │   ├── QuizActivity.kt    # Interactive quiz interface
│   │   │   └── ProgressActivity.kt # Progress tracking
│   │   └── res/
│   │       ├── layout/            # XML layouts
│   │       ├── drawable/          # Icons and graphics
│   │       ├── values/            # Colors, strings, themes
│   │       └── ...
│   └── build.gradle
├── build.gradle
├── settings.gradle
└── README.md
```

## Key Components

### Data Models
- **Level**: Represents a learning level with lessons and progress
- **Lesson**: Contains code content, explanations, and quiz questions
- **QuizQuestion**: Supports multiple choice and fill-in-the-blank formats
- **UserProgress**: Tracks completion status and scores

### Activities
- **MainActivity**: Level selection with progress overview
- **LessonActivity**: Displays code lessons with syntax highlighting
- **QuizActivity**: Interactive quiz interface with immediate feedback
- **ProgressActivity**: Comprehensive progress tracking

### Adapters
- **LevelAdapter**: Displays available levels with progress indicators
- **LessonAdapter**: Shows lesson content with code highlighting
- **ProgressAdapter**: Detailed progress breakdown by level

## Learning Content

### Level 1: ERC-20 Token Contract
- Token contract structure and state variables
- Transfer function implementation
- JavaScript testing with Hardhat
- Balance checking and event emission

### Level 2: ERC-721 NFT Contract
- Non-fungible token concepts
- Minting and ownership functions
- Metadata handling
- NFT-specific testing patterns

### Level 3: Crowdfunding via NFT (Planned)
- Crowdfunding contract mechanics
- NFT rewards system
- Goal tracking and fund distribution
- Advanced testing scenarios

### Level 4: DAO Contract (Planned)
- Governance token implementation
- Proposal creation and voting
- Execution mechanisms
- DAO testing strategies

### Level 5: AMM Contract & React Frontend (Planned)
- Automated Market Maker logic
- Liquidity pool management
- React/Redux frontend integration
- End-to-end testing

## Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24 or higher
- Kotlin 1.8.20+

### Installation
1. Clone the repository
2. Open the project in Android Studio
3. Sync Gradle files
4. Run the app on an emulator or device

### Building
```bash
./gradlew assembleDebug
```

### Testing
```bash
./gradlew test
```

## Dependencies

- **AndroidX Libraries**: Core, AppCompat, Material Design
- **RecyclerView**: For efficient list displays
- **CodeView**: Syntax highlighting for Solidity code
- **Gson**: JSON parsing for lesson data
- **Lifecycle Components**: ViewModel and LiveData

## Future Enhancements

- [ ] Complete levels 3, 4, and 5 with full content
- [ ] Add more interactive code editing features
- [ ] Implement achievement system
- [ ] Add social features for sharing progress
- [ ] Support for multiple programming languages
- [ ] Offline mode for lessons
- [ ] Advanced analytics and learning insights

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Based on real Solidity contracts from the workspace
- Inspired by interactive coding education platforms
- Uses OpenZeppelin contract standards for examples
