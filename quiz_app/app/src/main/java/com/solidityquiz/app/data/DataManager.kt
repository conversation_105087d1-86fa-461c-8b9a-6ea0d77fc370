package com.solidityquiz.app.data

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.solidityquiz.app.R
import com.solidityquiz.app.models.*

class DataManager(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("solidity_quiz_prefs", Context.MODE_PRIVATE)
    private val gson = Gson()
    
    fun getAllLevels(): List<Level> {
        return listOf(
            createLevel1(),
            createLevel2(),
            createLevel3(),
            createLevel4(),
            createLevel5()
        ).map { level ->
            level.copy(
                isUnlocked = isLevelUnlocked(level.id),
                isCompleted = isLevelCompleted(level.id),
                progress = getLevelProgress(level.id)
            )
        }
    }
    
    fun getLevelById(levelId: Int): Level? {
        return getAllLevels().find { it.id == levelId }
    }
    
    fun getLessonById(levelId: Int, lessonId: Int): Lesson? {
        return getLevelById(levelId)?.lessons?.find { it.id == lessonId }
    }
    
    private fun createLevel1(): Level {
        val lessons = listOf(
            Lesson(
                id = 1,
                levelId = 1,
                title = "ERC-20 Token Basics",
                description = "Learn the fundamentals of ERC-20 token contracts",
                codeContent = """
                    pragma solidity ^0.8.0;
                    
                    contract Token {
                        string public name;
                        string public symbol;
                        uint256 public totalSupply;
                        
                        mapping(address => uint256) public balanceOf;
                    }
                """.trimIndent(),
                explanation = "This is the basic structure of an ERC-20 token. The contract defines the token's name, symbol, total supply, and a mapping to track balances.",
                quizQuestions = createLevel1Questions()
            ),
            Lesson(
                id = 2,
                levelId = 1,
                title = "Token Transfer Function",
                description = "Implement the transfer functionality",
                codeContent = """
                    function transfer(address _to, uint256 _value) 
                        public returns (bool success) {
                        require(balanceOf[msg.sender] >= _value);
                        balanceOf[msg.sender] -= _value;
                        balanceOf[_to] += _value;
                        emit Transfer(msg.sender, _to, _value);
                        return true;
                    }
                """.trimIndent(),
                explanation = "The transfer function allows users to send tokens to other addresses. It checks the sender has enough balance and updates both accounts.",
                quizQuestions = createLevel1TransferQuestions()
            )
        )
        
        return Level(
            id = 1,
            title = "ERC-20 Token Contract",
            description = "Build your first smart contract - a simple ERC-20 token with JavaScript testing",
            iconResource = R.drawable.ic_token,
            lessons = lessons,
            isUnlocked = true // First level is always unlocked
        )
    }
    
    private fun createLevel2(): Level {
        val lessons = listOf(
            Lesson(
                id = 1,
                levelId = 2,
                title = "ERC-721 NFT Basics",
                description = "Understanding Non-Fungible Tokens",
                codeContent = """
                    pragma solidity ^0.8.0;

                    import "@openzeppelin/contracts/token/ERC721/ERC721.sol";

                    contract MyNFT is ERC721 {
                        uint256 private _tokenIdCounter;

                        constructor() ERC721("MyNFT", "MNFT") {}

                        function safeMint(address to) public {
                            uint256 tokenId = _tokenIdCounter;
                            _tokenIdCounter++;
                            _safeMint(to, tokenId);
                        }
                    }
                """.trimIndent(),
                explanation = "ERC-721 tokens are unique, non-fungible tokens. Each token has a unique ID and can represent ownership of digital assets. The safeMint function creates new tokens with incremental IDs.",
                quizQuestions = createLevel2Questions()
            ),
            Lesson(
                id = 2,
                levelId = 2,
                title = "NFT Metadata and Testing",
                description = "Adding metadata and writing JavaScript tests",
                codeContent = """
                    // JavaScript test for NFT contract
                    const { expect } = require('chai');
                    const { ethers } = require('hardhat');

                    describe('MyNFT', function () {
                        let nft, owner, addr1;

                        beforeEach(async function () {
                            [owner, addr1] = await ethers.getSigners();
                            const MyNFT = await ethers.getContractFactory('MyNFT');
                            nft = await MyNFT.deploy();
                        });

                        it('Should mint NFT correctly', async function () {
                            await nft.safeMint(addr1.address);
                            expect(await nft.ownerOf(0)).to.equal(addr1.address);
                        });
                    });
                """.trimIndent(),
                explanation = "Testing NFT contracts involves verifying minting functionality, ownership transfers, and metadata handling. Each test should check specific contract behaviors.",
                quizQuestions = createLevel2TestQuestions()
            )
        )

        return Level(
            id = 2,
            title = "ERC-721 NFT Contract",
            description = "Create unique digital assets with NFT contracts and comprehensive testing",
            iconResource = R.drawable.ic_nft,
            lessons = lessons
        )
    }
    
    private fun createLevel3(): Level {
        return Level(
            id = 3,
            title = "Crowdfunding via NFT",
            description = "Build a crowdfunding platform that mints NFTs as rewards",
            iconResource = R.drawable.ic_crowdfunding,
            lessons = emptyList() // Will be populated later
        )
    }
    
    private fun createLevel4(): Level {
        return Level(
            id = 4,
            title = "DAO Contract",
            description = "Create a decentralized autonomous organization with voting mechanisms",
            iconResource = R.drawable.ic_dao,
            lessons = emptyList() // Will be populated later
        )
    }
    
    private fun createLevel5(): Level {
        return Level(
            id = 5,
            title = "AMM Contract & React Frontend",
            description = "Build an automated market maker with React/Redux frontend integration",
            iconResource = R.drawable.ic_amm,
            lessons = emptyList() // Will be populated later
        )
    }

    private fun createLevel1Questions(): List<QuizQuestion> {
        return listOf(
            QuizQuestion(
                id = 1,
                lessonId = 1,
                type = QuestionType.FILL_IN_THE_BLANK,
                question = "Complete the ERC-20 token contract structure:",
                codeSnippet = """
                    contract Token {
                        string public ___;
                        string public ___;
                        uint256 public ___;
                        mapping(address => uint256) public ___;
                    }
                """.trimIndent(),
                correctAnswer = "name,symbol,totalSupply,balanceOf",
                explanation = "These are the essential state variables for an ERC-20 token contract.",
                blanks = listOf(
                    BlankField(1, "token name", "name", 1),
                    BlankField(2, "token symbol", "symbol", 2),
                    BlankField(3, "total supply", "totalSupply", 3),
                    BlankField(4, "balance mapping", "balanceOf", 4)
                )
            ),
            QuizQuestion(
                id = 2,
                lessonId = 1,
                type = QuestionType.MULTIPLE_CHOICE,
                question = "What data type is used for token balances in Solidity?",
                options = listOf("int256", "uint256", "string", "bool"),
                correctAnswer = "uint256",
                explanation = "uint256 is used because token balances should never be negative and can be very large numbers."
            )
        )
    }

    private fun createLevel1TransferQuestions(): List<QuizQuestion> {
        return listOf(
            QuizQuestion(
                id = 3,
                lessonId = 2,
                type = QuestionType.FILL_IN_THE_BLANK,
                question = "Complete the transfer function:",
                codeSnippet = """
                    function transfer(address _to, uint256 _value) public returns (bool) {
                        require(balanceOf[___] >= ___);
                        balanceOf[msg.sender] -= ___;
                        balanceOf[___] += ___;
                        return true;
                    }
                """.trimIndent(),
                correctAnswer = "msg.sender,_value,_value,_to,_value",
                explanation = "The transfer function checks sender balance, deducts from sender, and adds to recipient.",
                blanks = listOf(
                    BlankField(1, "sender address", "msg.sender", 1),
                    BlankField(2, "transfer amount", "_value", 2),
                    BlankField(3, "deduct amount", "_value", 3),
                    BlankField(4, "recipient address", "_to", 4),
                    BlankField(5, "add amount", "_value", 5)
                )
            )
        )
    }

    private fun createLevel2Questions(): List<QuizQuestion> {
        return listOf(
            QuizQuestion(
                id = 4,
                lessonId = 1,
                type = QuestionType.MULTIPLE_CHOICE,
                question = "What makes ERC-721 tokens different from ERC-20 tokens?",
                options = listOf(
                    "They are fungible",
                    "They are non-fungible and unique",
                    "They cannot be transferred",
                    "They don't have metadata"
                ),
                correctAnswer = "They are non-fungible and unique",
                explanation = "ERC-721 tokens are non-fungible, meaning each token is unique and cannot be replaced by another token."
            ),
            QuizQuestion(
                id = 5,
                lessonId = 1,
                type = QuestionType.FILL_IN_THE_BLANK,
                question = "Complete the NFT minting function:",
                codeSnippet = """
                    function safeMint(address to) public {
                        uint256 tokenId = ___;
                        ___++;
                        _safeMint(___, ___);
                    }
                """.trimIndent(),
                correctAnswer = "_tokenIdCounter,_tokenIdCounter,to,tokenId",
                explanation = "The safeMint function uses a counter for unique token IDs and calls _safeMint with the recipient and token ID.",
                blanks = listOf(
                    BlankField(1, "counter variable", "_tokenIdCounter", 1),
                    BlankField(2, "increment counter", "_tokenIdCounter", 2),
                    BlankField(3, "recipient address", "to", 3),
                    BlankField(4, "token ID", "tokenId", 4)
                )
            )
        )
    }

    private fun createLevel2TestQuestions(): List<QuizQuestion> {
        return listOf(
            QuizQuestion(
                id = 6,
                lessonId = 2,
                type = QuestionType.MULTIPLE_CHOICE,
                question = "What function is used to check the owner of an NFT?",
                options = listOf("balanceOf", "ownerOf", "tokenURI", "approve"),
                correctAnswer = "ownerOf",
                explanation = "The ownerOf function returns the address that owns a specific token ID."
            ),
            QuizQuestion(
                id = 7,
                lessonId = 2,
                type = QuestionType.FILL_IN_THE_BLANK,
                question = "Complete the NFT test:",
                codeSnippet = """
                    it('Should mint NFT correctly', async function () {
                        await nft.___(___.address);
                        expect(await nft.___(0)).to.equal(___.address);
                    });
                """.trimIndent(),
                correctAnswer = "safeMint,addr1,ownerOf,addr1",
                explanation = "The test calls safeMint to create an NFT and checks ownership with ownerOf.",
                blanks = listOf(
                    BlankField(1, "mint function", "safeMint", 1),
                    BlankField(2, "recipient", "addr1", 2),
                    BlankField(3, "owner check function", "ownerOf", 3),
                    BlankField(4, "expected owner", "addr1", 4)
                )
            )
        )
    }

    // Progress tracking methods
    fun isLevelUnlocked(levelId: Int): Boolean {
        if (levelId == 1) return true // First level always unlocked
        val previousLevelCompleted = isLevelCompleted(levelId - 1)
        return sharedPreferences.getBoolean("level_${levelId}_unlocked", previousLevelCompleted)
    }

    fun isLevelCompleted(levelId: Int): Boolean {
        return sharedPreferences.getBoolean("level_${levelId}_completed", false)
    }

    fun getLevelProgress(levelId: Int): Int {
        return sharedPreferences.getInt("level_${levelId}_progress", 0)
    }

    fun markLessonCompleted(levelId: Int, lessonId: Int) {
        sharedPreferences.edit()
            .putBoolean("lesson_${levelId}_${lessonId}_completed", true)
            .apply()
        updateLevelProgress(levelId)
    }

    private fun updateLevelProgress(levelId: Int) {
        val level = getLevelById(levelId) ?: return
        val completedLessons = level.lessons.count { lesson ->
            sharedPreferences.getBoolean("lesson_${levelId}_${lesson.id}_completed", false)
        }
        val progress = (completedLessons * 100) / level.lessons.size

        sharedPreferences.edit()
            .putInt("level_${levelId}_progress", progress)
            .apply()

        if (progress == 100) {
            sharedPreferences.edit()
                .putBoolean("level_${levelId}_completed", true)
                .putBoolean("level_${levelId + 1}_unlocked", true)
                .apply()
        }
    }
}
