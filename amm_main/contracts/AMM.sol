// SPDX-License-Identifier: Unlicense
pragma solidity ^0.8.20;

import "hardhat/console.sol";
import "./Token.sol";

/**
 * @title Automated Market Maker (AMM)
 * @dev Implements a constant product market maker using the formula x * y = k
 * 
 * Contract Operation Flow:
 * 1. Contract is initialized with two ERC20 token contracts
 * 2. Liquidity providers can:
 *    - Add initial liquidity at any ratio
 *    - Add subsequent liquidity matching current pool ratio
 *    - Remove liquidity and receive both tokens proportionally
 * 3. Traders can:
 *    - Swap tokenA for tokenB
 *    - Swap tokenB for tokenA
 * 
 * Key Features:
 * - Maintains constant product invariant (k = x * y)
 * - Tracks liquidity provider shares
 * - Ensures proportional liquidity additions
 * - Prevents complete pool drainage
 * 
 * Dependencies:
 * - Requires ERC20 Token contracts for both tokens
 * - Uses Hardhat console for debugging (development only)
 */
contract AutomatedMarketMaker {
    // External token contract interfaces
    Token public tokenA;
    Token public tokenB;

    // Pool state variables
    uint256 public tokenAReserve;    // Current reserve of tokenA in the pool
    uint256 public tokenBReserve;    // Current reserve of tokenB in the pool
    uint256 public constantProduct;   // Invariant k in x * y = k formula

    // Liquidity accounting variables
    uint256 public totalLiquidityShares;                    // Total shares issued
    mapping(address => uint256) public userLiquidityShares; // User share balances
    uint256 constant PRECISION = 10**18;                    // Precision factor for share calculations

    
    // @dev Emitted when a token swap occurs
    event Swap(
        address indexed user,
        address tokenProvided,
        uint256 amountProvided,
        address tokenReceived,
        uint256 amountReceived,
        uint256 newTokenAReserve,
        uint256 newTokenBReserve,
        uint256 timestamp
    );

    
       /**
     * @dev Contract initializer
     * @notice Requires external ERC20 token contracts to be deployed first
     * @param _tokenA Address of the first ERC20 token contract
     * @param _tokenB Address of the second ERC20 token contract
     */
    constructor(Token _tokenA, Token _tokenB) {
        tokenA = _tokenA;
        tokenB = _tokenB;
    }

    /**
     * @dev Adds liquidity to the trading pool
     * @notice Uses external ERC20 transferFrom() to receive tokens from user
     * @param _tokenAAmount Amount of first token to deposit
     * @param _tokenBAmount Amount of second token to deposit
     */
    function addLiquidity(uint256 _tokenAAmount, uint256 _tokenBAmount) external {
        // External calls to ERC20 token contracts
        require(
            tokenA.transferFrom(msg.sender, address(this), _tokenAAmount),
            "Failed to transfer tokenA to the pool"
        );
        require(
            tokenB.transferFrom(msg.sender, address(this), _tokenBAmount),
            "Failed to transfer tokenB to the pool"
        );

        uint256 liquiditySharestoMint;

        // Initial liquidity provision
        if (totalLiquidityShares == 0) {
            liquiditySharestoMint = 100 * PRECISION;
        } else {
            // Calculate proportional shares for subsequent deposits
            uint256 proportionalSharesFromA = (totalLiquidityShares * _tokenAAmount) / tokenAReserve;
            uint256 proportionalSharesFromB = (totalLiquidityShares * _tokenBAmount) / tokenBReserve;
            
            // Verify deposit ratio matches current pool ratio (allowing 0.1% deviation)
            require(
                (proportionalSharesFromA / 10**3) == (proportionalSharesFromB / 10**3),
                "Must provide tokens in the current pool ratio"
            );
            liquiditySharestoMint = proportionalSharesFromA;
        }

        // Update pool reserves and constant product
        tokenAReserve += _tokenAAmount;
        tokenBReserve += _tokenBAmount;
        constantProduct = tokenAReserve * tokenBReserve;

        // Update liquidity accounting
        totalLiquidityShares += liquiditySharestoMint;
        userLiquidityShares[msg.sender] += liquiditySharestoMint;
    }

    // @dev Calculates how much tokenB should be deposited given an amount of tokenA
    // @param _tokenAAmount Amount of tokenA to deposit
    // @return requiredTokenBAmount Amount of tokenB required to maintain the current ratio
    function calculateTokenBDeposit(uint256 _tokenAAmount)
        public
        view
        returns (uint256 requiredTokenBAmount)
    {
        requiredTokenBAmount = (tokenBReserve * _tokenAAmount) / tokenAReserve;
    }

    // @dev Calculates how much tokenA should be deposited given an amount of tokenB
    // @param _tokenBAmount Amount of tokenB to deposit
    // @return requiredTokenAAmount Amount of tokenA required to maintain the current ratio
    function calculateTokenADeposit(uint256 _tokenBAmount)
        public
        view
        returns (uint256 requiredTokenAAmount)
    {
        requiredTokenAAmount = (tokenAReserve * _tokenBAmount) / tokenBReserve;
    }

    // @dev Calculates how much tokenB will be received when swapping tokenA
    // @param _tokenAAmount Amount of tokenA to swap
    // @return tokenBOutput Amount of tokenB that will be received
    // @notice Uses the constant product formula: x * y = k
    function calculateTokenASwap(uint256 _tokenAAmount)
        public
        view
        returns (uint256 tokenBOutput)
    {
        uint256 tokenAAfterSwap = tokenAReserve + _tokenAAmount;
        uint256 tokenBAfterSwap = constantProduct / tokenAAfterSwap;
        tokenBOutput = tokenBReserve - tokenBAfterSwap;

        // Safety check: Don't drain the entire pool
        if (tokenBOutput == tokenBReserve) {
            tokenBOutput--;
        }

        require(tokenBOutput < tokenBReserve, "Swap amount too large for pool reserves");
    }

    // @dev Swaps tokenA for tokenB
    // @param _tokenAAmount Amount of tokenA to swap
    // @return tokenBOutput Amount of tokenB received from the swap
    function swapTokenA(uint256 _tokenAAmount)
        external
        returns(uint256 tokenBOutput)
    {
        // Calculate the amount of tokenB to be received
        tokenBOutput = calculateTokenASwap(_tokenAAmount);

        // Execute the swap
        tokenA.transferFrom(msg.sender, address(this), _tokenAAmount);
        tokenAReserve += _tokenAAmount;
        tokenBReserve -= tokenBOutput;
        tokenB.transfer(msg.sender, tokenBOutput);

        // Emit swap event
        emit Swap(
            msg.sender,
            address(tokenA),
            _tokenAAmount,
            address(tokenB),
            tokenBOutput,
            tokenAReserve,
            tokenBReserve,
            block.timestamp
        );
    }

    
    // @dev Calculates how much tokenA will be received when swapping tokenB
    // @param _tokenBAmount Amount of tokenB to swap
    // @return tokenAOutput Amount of tokenA that will be received
    // @notice Uses the constant product formula: x * y = k
    function calculateTokenBSwap(uint256 _tokenBAmount)
        public
        view
        returns (uint256 tokenAOutput)
    {
        uint256 tokenBAfterSwap = tokenBReserve + _tokenBAmount;
        uint256 tokenAAfterSwap = constantProduct / tokenBAfterSwap;
        tokenAOutput = tokenAReserve - tokenAAfterSwap;

        // Safety check: Don't drain the entire pool
        if (tokenAOutput == tokenAReserve) {
            tokenAOutput--;
        }

        require(tokenAOutput < tokenAReserve, "Swap amount too large for pool reserves");
    }

    // @dev Swaps tokenB for tokenA
    // @param _tokenBAmount Amount of tokenB to swap
    // @return tokenAOutput Amount of tokenA received from the swap
    function swapTokenB(uint256 _tokenBAmount)
        external
        returns(uint256 tokenAOutput)
    {
        // Calculate the amount of tokenA to be received
        tokenAOutput = calculateTokenBSwap(_tokenBAmount);

        // Execute the swap
        tokenB.transferFrom(msg.sender, address(this), _tokenBAmount);
        tokenBReserve += _tokenBAmount;
        tokenAReserve -= tokenAOutput;
        tokenA.transfer(msg.sender, tokenAOutput);

        // Emit swap event
        emit Swap(
            msg.sender,
            address(tokenB),
            _tokenBAmount,
            address(tokenA),
            tokenAOutput,
            tokenAReserve,
            tokenBReserve,
            block.timestamp
        );
    }

    // Calculates how many tokens will be withdrawn for a given amount of shares
    // @param _sharesToWithdraw Number of liquidity shares to withdraw
    // @return tokenAAmount Amount of tokenA to be withdrawn
    // @return tokenBAmount Amount of tokenB to be withdrawn
    function calculateWithdrawAmount(uint256 _sharesToWithdraw)
        public
        view
        returns (uint256 tokenAAmount, uint256 tokenBAmount)
    {
        require(_sharesToWithdraw <= totalLiquidityShares, "Cannot withdraw more than total shares");
        
        // Calculate withdrawal amounts proportional to shares
        tokenAAmount = (_sharesToWithdraw * tokenAReserve) / totalLiquidityShares;
        tokenBAmount = (_sharesToWithdraw * tokenBReserve) / totalLiquidityShares;
    }

    // Removes liquidity from the pool
    // @param _sharesToWithdraw Number of liquidity shares to withdraw
    // @return tokenAAmount Amount of tokenA withdrawn
    // @return tokenBAmount Amount of tokenB withdrawn
    function removeLiquidity(uint256 _sharesToWithdraw)
        external
        returns(uint256 tokenAAmount, uint256 tokenBAmount)
    {
        require(
            _sharesToWithdraw <= userLiquidityShares[msg.sender],
            "Cannot withdraw more shares than you own"
        );

        // Calculate token amounts to withdraw
        (tokenAAmount, tokenBAmount) = calculateWithdrawAmount(_sharesToWithdraw);

        // Update state
        userLiquidityShares[msg.sender] -= _sharesToWithdraw;
        totalLiquidityShares -= _sharesToWithdraw;

        tokenAReserve -= tokenAAmount;
        tokenBReserve -= tokenBAmount;
        constantProduct = tokenAReserve * tokenBReserve;

        // Transfer tokens to user
        tokenA.transfer(msg.sender, tokenAAmount);
        tokenB.transfer(msg.sender, tokenBAmount);
    }
}